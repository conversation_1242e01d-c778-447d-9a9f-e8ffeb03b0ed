name: Bug Report
description: Clearly report a bug with detailed repro steps
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        **Thanks for your report!** Please check existing issues first:  
        👉 https://github.com/RooCodeInc/Roo-Code/issues

  - type: input
    id: version
    attributes:
      label: App Version
      description: What version of Roo Code are you using? (e.g., v3.3.1)
    validations:
      required: true

  - type: dropdown
    id: provider
    attributes:
      label: API Provider
      options:
        - Anthropic
        - AWS Bedrock
        - Chutes AI
        - DeepSeek
        - Glama
        - Google Gemini
        - Google Vertex AI
        - Groq
        - Human Relay Provider
        - LiteLLM
        - LM Studio
        - Mistral AI
        - Ollama
        - OpenAI
        - OpenAI Compatible
        - OpenRouter
        - Requesty
        - Unbound
        - VS Code Language Model API
        - xAI (Grok)
        - Not Applicable / Other
    validations:
      required: true

  - type: input
    id: model
    attributes:
      label: Model Used
      description: Exact model name (e.g., Claude 3.7 Sonnet). Use N/A if irrelevant.
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: 🔁 Steps to Reproduce
      description: |
        Help us see what you saw. Give clear, numbered steps:

        1. Setup (OS, extension version, settings)
        2. Exact actions (clicks, input, files, commands)
        3. What happened after each step

        Think like you're writing a recipe. Without this, we can't reproduce the issue.
    validations:
      required: true

  - type: textarea
    id: what-happened
    attributes:
      label: 💥 Outcome Summary
      description: |
        Recap what went wrong in one or two lines.

        Example: "Expected code to run, but got an empty response and no error."
      placeholder: Expected ___, but got ___.
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: 📄 Relevant Logs or Errors (Optional)
      description: Paste API logs, terminal output, or errors here. Use triple backticks (```) for code formatting.
      render: shell