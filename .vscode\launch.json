// A launch configuration that compiles the extension and then opens it inside a new window
// Use IntelliSense to learn about possible attributes.
// Hover to view descriptions of existing attributes.
// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "Run Extension",
			"type": "extensionHost",
			"request": "launch",
			"runtimeExecutable": "${execPath}",
			"args": ["--extensionDevelopmentPath=${workspaceFolder}/src"],
			"sourceMaps": true,
			"outFiles": ["${workspaceFolder}/src/dist/**/*.js"],
			"preLaunchTask": "${defaultBuildTask}",
			"env": {
				"NODE_ENV": "development",
				"VSCODE_DEBUG_MODE": "true"
			},
			"resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"],
			"presentation": {
				"hidden": false,
				"group": "tasks",
				"order": 1
			}
		}
	]
}
