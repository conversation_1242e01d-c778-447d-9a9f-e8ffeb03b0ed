{"version": "6", "dialect": "sqlite", "id": "c0fa8491-b5c0-493d-aa32-ddf280259c30", "prevId": "00000000-0000-0000-0000-000000000000", "tables": {"runs": {"name": "runs", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "taskMetricsId": {"name": "taskMetricsId", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "model": {"name": "model", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "pid": {"name": "pid", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "socketPath": {"name": "socketPath", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "passed": {"name": "passed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "failed": {"name": "failed", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {"runs_taskMetricsId_taskMetrics_id_fk": {"name": "runs_taskMetricsId_taskMetrics_id_fk", "tableFrom": "runs", "tableTo": "taskMetrics", "columnsFrom": ["taskMetricsId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "taskMetrics": {"name": "taskMetrics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "tokensIn": {"name": "tokensIn", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "tokensOut": {"name": "tokensOut", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "tokensContext": {"name": "tokensContext", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cacheWrites": {"name": "cacheWrites", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cacheReads": {"name": "cacheReads", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "tasks": {"name": "tasks", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "runId": {"name": "runId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "taskMetricsId": {"name": "taskMetricsId", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "exercise": {"name": "exercise", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "passed": {"name": "passed", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "startedAt": {"name": "startedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "finishedAt": {"name": "finishedAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"tasks_language_exercise_idx": {"name": "tasks_language_exercise_idx", "columns": ["runId", "language", "exercise"], "isUnique": true}}, "foreignKeys": {"tasks_runId_runs_id_fk": {"name": "tasks_runId_runs_id_fk", "tableFrom": "tasks", "tableTo": "runs", "columnsFrom": ["runId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "tasks_taskMetricsId_taskMetrics_id_fk": {"name": "tasks_taskMetricsId_taskMetrics_id_fk", "tableFrom": "tasks", "tableTo": "taskMetrics", "columnsFrom": ["taskMetricsId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}