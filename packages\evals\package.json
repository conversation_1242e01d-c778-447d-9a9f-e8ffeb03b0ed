{"name": "@roo-code/evals", "description": "Roo Code evals.", "version": "0.0.0", "type": "module", "exports": "./src/index.ts", "scripts": {"lint": "eslint src --ext=ts --max-warnings=0", "check-types": "tsc --noEmit", "_test": "dotenvx run -f .env.test -- vitest run", "clean": "rimraf dist .turbo", "cli": "dotenvx run -f .env.development .env.local -- tsx src/cli/index.ts", "drizzle-kit": "dotenvx run -f .env.development -- tsx node_modules/drizzle-kit/bin.cjs", "drizzle-kit:test": "dotenvx run -f .env.test -- tsx node_modules/drizzle-kit/bin.cjs", "db:generate": "pnpm drizzle-kit generate", "db:migrate": "pnpm drizzle-kit migrate", "db:push": "pnpm drizzle-kit push", "db:check": "pnpm drizzle-kit check", "db:test:push": "pnpm drizzle-kit:test push", "db:test:check": "pnpm drizzle-kit:test check", "db:start": "docker compose up -d db", "db:stop": "docker compose down db", "redis:start": "docker compose up -d redis", "redis:stop": "docker compose down redis"}, "dependencies": {"@roo-code/ipc": "workspace:^", "@roo-code/types": "workspace:^", "better-sqlite3": "^11.10.0", "cmd-ts": "^0.13.0", "drizzle-orm": "^0.44.1", "execa": "^9.6.0", "node-ipc": "^12.0.0", "p-map": "^7.0.3", "p-queue": "^8.1.0", "p-wait-for": "^5.0.2", "postgres": "^3.4.7", "ps-tree": "^1.2.0", "redis": "^5.5.5", "zod": "^3.24.2"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@types/node": "^22.15.20", "@types/node-ipc": "^9.2.3", "@types/ps-tree": "^1.1.6", "drizzle-kit": "^0.31.1", "tsx": "^4.19.3", "vitest": "^3.2.0"}}